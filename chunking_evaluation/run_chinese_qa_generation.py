"""
中文问答生成运行脚本

这个脚本整合了数据集路径收集和中文问答生成功能。
专门用于为中文数据集生成问答对。
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.evaluation_framework.synthetic_evaluation_chinese import SyntheticEvaluationChinese

def get_all_dataset_paths():
    """
    获取datasets目录下所有.txt文件的路径
    
    返回:
        list: 所有文本文件的绝对路径列表
    """
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    
    # 存储所有文件路径
    all_paths = []
    
    # 遍历datasets目录下的所有子目录
    for subdir in ['法律文件', '国家政策']:
        subdir_path = current_dir / "datasets" / subdir
        if subdir_path.exists():
            # 获取该子目录下所有.txt文件
            txt_files = list(subdir_path.glob('*.txt'))
            all_paths.extend([str(f) for f in txt_files])
    
    return all_paths

def print_dataset_info():
    """
    打印数据集信息
    """
    paths = get_all_dataset_paths()
    print(f"找到 {len(paths)} 个文本文件:")
    
    # 按目录分组显示
    legal_files = [p for p in paths if '法律文件' in p]
    policy_files = [p for p in paths if '国家政策' in p]
    
    print(f"\n法律文件 ({len(legal_files)} 个):")
    for path in legal_files:
        filename = os.path.basename(path)
        print(f"  - {filename}")
    
    print(f"\n国家政策 ({len(policy_files)} 个):")
    for path in policy_files:
        filename = os.path.basename(path)
        print(f"  - {filename}")

def generate_chinese_qa():
    """
    为中文数据集生成问答对
    """
    # 获取所有数据集路径
    dataset_paths = get_all_dataset_paths()
    
    if not dataset_paths:
        print("未找到任何文本文件！")
        return
    
    print(f"找到 {len(dataset_paths)} 个文本文件")
    
    # 设置输出文件路径
    output_dir = Path(__file__).parent / "evaluation_framework" / "chinese_qa_data"
    output_dir.mkdir(exist_ok=True)
    
    questions_csv_path = output_dir / "chinese_questions.csv"
    
    # 创建SyntheticEvaluationChinese实例
    print("初始化中文问答生成器...")
    synthetic_eval = SyntheticEvaluationChinese(
        corpora_paths=dataset_paths,
        queries_csv_path=str(questions_csv_path)
    )
    
    # 生成问答对
    print("开始生成问答对...")
    print("参数设置:")
    print("- 近似模式: False (使用精确模式)")
    print("- 每个语料库问题数: 5")
    print("- 生成轮数: 1")
    
    synthetic_eval.generate_queries_and_excerpts(
        approximate_excerpts=False,  # 使用精确模式
        num_rounds=1,               # 生成1轮
        queries_per_corpus=5        # 每个语料库生成5个问题
    )
    
    print(f"问答对生成完成！")
    print(f"输出文件: {questions_csv_path}")
    
    # 显示生成统计
    if os.path.exists(questions_csv_path):
        import pandas as pd
        df = pd.read_csv(questions_csv_path, encoding='utf-8')
        print(f"\n生成统计:")
        print(f"- 总问题数: {len(df)}")
        print(f"- 涉及语料库数: {df['corpus_id'].nunique()}")
        
        # 按目录统计
        legal_count = len(df[df['corpus_id'].str.contains('法律文件')])
        policy_count = len(df[df['corpus_id'].str.contains('国家政策')])
        print(f"- 法律文件问题数: {legal_count}")
        print(f"- 国家政策问题数: {policy_count}")

def filter_chinese_qa():
    """
    过滤中文问答对的质量
    """
    output_dir = Path(__file__).parent / "evaluation_framework" / "chinese_qa_data"
    questions_csv_path = output_dir / "chinese_questions.csv"
    
    if not os.path.exists(questions_csv_path):
        print("问答文件不存在，请先生成问答对！")
        return
    
    # 获取数据集路径
    dataset_paths = get_all_dataset_paths()
    
    # 创建SyntheticEvaluationChinese实例
    synthetic_eval = SyntheticEvaluationChinese(
        corpora_paths=dataset_paths,
        queries_csv_path=str(questions_csv_path)
    )
    
    print("开始过滤低质量问答对...")
    # 过滤低质量问答对（相似度阈值0.36）
    synthetic_eval.filter_poor_excerpts(threshold=0.36)
    
    print("开始过滤重复问题...")
    # 过滤重复问题（相似度阈值0.78）
    synthetic_eval.filter_duplicates(threshold=0.78)
    
    print("过滤完成！")
    
    # 显示过滤后统计
    import pandas as pd
    df = pd.read_csv(questions_csv_path, encoding='utf-8')
    print(f"\n过滤后统计:")
    print(f"- 剩余问题数: {len(df)}")
    print(f"- 涉及语料库数: {df['corpus_id'].nunique()}")

def main():
    """
    主函数：生成和过滤中文问答对
    """
    print("=== 中文数据集问答生成工具 ===")
    print()
    
    # 第一步：生成问答对
    print("第一步：生成问答对")
    print("-" * 30)
    generate_chinese_qa()
    print()
    
    # 第二步：过滤问答对
    print("第二步：过滤问答对")
    print("-" * 30)
    filter_chinese_qa()
    print()
    
    print("=== 所有操作完成！ ===")
    print()
    print("生成的文件位置：")
    print("chunking_evaluation/evaluation_framework/chinese_qa_data/chinese_questions.csv")
    print()
    print("你可以使用以下命令查看生成的问题：")
    print("python -c \"import pandas as pd; df = pd.read_csv('chunking_evaluation/evaluation_framework/chinese_qa_data/chinese_questions.csv', encoding='utf-8'); print(df.head())\"")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="中文数据集问答生成工具")
    parser.add_argument("--action", choices=["generate", "filter", "both", "info"], 
                       default="both", help="执行的操作")
    
    args = parser.parse_args()
    
    if args.action == "info":
        print("=== 数据集信息 ===")
        print_dataset_info()
    elif args.action in ["generate", "both"]:
        print("=== 开始生成问答对 ===")
        generate_chinese_qa()
        print()
    
    if args.action in ["filter", "both"]:
        print("=== 开始过滤问答对 ===")
        filter_chinese_qa()
        print()
    
    if args.action == "both":
        print("所有操作完成！") 